clear all;

%-------------------------------------------------------------------------
% Raw data input
%-------------------------------------------------------------------------
fileID = fopen('Success_Packets_20250715_104824\Success_Packet_001.bin');   %5 %17 %33
%fileID = fopen('CRC_Error_Data_20250715_104824\CRC_Error_Packet_001.bin');   %9실패, 17,22복구성공
source_data = fread(fileID, 'uint16');

%-------------------------------------------------------------------------
% Defines
%-------------------------------------------------------------------------
OSR = 5;                                % Over sampling rate
BT = 0.4;                               % Bandwidth time

PACKET_PERIOD = 198;                    % Packet period
PACKET_PERIOD_OS = PACKET_PERIOD*OSR;
LEN_PSF = 8*OSR;                        % Pulse shaping filter length

DC_LEVEL = 0;                           % DC level
NUM_OF_PACKETS = 1;                     % Number of packets

%-------------------------------------------------------------------------
% Traning sequence(24bit : 0101...) and start flag(0x7E)
%-------------------------------------------------------------------------
dot_pattern       = repmat([-1, -1, 1, 1], 1, 6);   % Dot pattern
start_pattern     = [-1, -1, -1, -1, -1, -1, -1, 1];% Start pattern
preamble          = [dot_pattern, start_pattern]';  % Preamble
preamble_os       = repelem(preamble, OSR);         % Over sampled preamble
LEN_DOT_PATTERN   = length(dot_pattern);            % Length of dot pattern
LEN_START_PATTERN = length(start_pattern);          % Length of start pattern
LEN_PREAMBLE      = length(preamble);               % Length of preamble
LEN_PREAMBLE_OS   = LEN_PREAMBLE*OSR;               % Length of over sampled preamble

%-------------------------------------------------------------------------
% impulse response of gmsk, gauss filter
%-------------------------------------------------------------------------
[impulse_response_of_gmsk, kkk] = gmsk_impulse_response(BT, OSR, LEN_PSF, 2);
preamble_zero_padded            = upsample(preamble, OSR);
preamble_filtered_by_gmsk       = conv(preamble_zero_padded, impulse_response_of_gmsk);
preamble_range                  = 1:length(preamble_filtered_by_gmsk);

%-------------------------------------------------------------------------
figure(1);
subplot(4,1,1); plot(impulse_response_of_gmsk, '-o'); grid; title('impulse\_response (gmsk)');
subplot(4,1,2); plot(preamble, '-o'); grid; title('preamble');
subplot(4,1,3); plot(preamble_range, preamble_filtered_by_gmsk(preamble_range), '-o');
                    grid; title('preamble\_filtered (gmsk) (o), (gmsk twice) (x)');
subplot(4,1,4); plot(source_data, '-o'); grid; title('source\_data');
%-------------------------------------------------------------------------

%-------------------------------------------------------------------------
% functions
%-------------------------------------------------------------------------
% viterbi_mlsd
% Maximum Likelihood Sequence Detection (최대우도 시퀀스 검출) 함수

% Viterbi 알고리즘을 사용하여 ISI(Inter-Symbol Interference)가 있는 채널에서
% 최적의 비트 시퀀스를 복원하는 함수
%
% 입력 파라미터:
%   rx_data                       : 수신된 신호 데이터
%   h0                            : 채널 임펄스 응답의 중심값 (현재 심볼)
%   h1                            : 채널 임펄스 응답의 이전/이후 심볼 영향값
%   LOCAL_RANGE_ONE_PERIOD_BAUD_RATE : 처리할 샘플 범위
%
% 출력 파라미터:
%   data_seq_00                   : 복원된 비트 시퀀스 (상태 00에서 종료)
%
% 동작 원리:
% 1. 4개 상태 (00, 01, 10, 11)를 가진 트렐리스 구조 사용
% 2. 각 상태는 이전 2비트의 조합을 나타냄 (메모리 길이 = 2)
% 3. ISI를 고려한 8가지 예상 신호값을 미리 계산
% 4. Viterbi 알고리즘으로 최소 누적 메트릭 경로를 추적
function [data_seq_00] = viterbi_mlsd(rx_data, h0, h1)

    % ISI를 고려한 8가지 예상 신호 값 계산
    % 형태: [이전비트, 현재비트, 다음비트]의 조합에 따른 신호값
    % h_values(1): [-1,-1,-1] → -h1-h0-h1
    % h_values(2): [-1,-1,+1] → -h1-h0+h1
    % h_values(3): [-1,+1,-1] → -h1+h0-h1
    % h_values(4): [-1,+1,+1] → -h1+h0+h1
    % h_values(5): [+1,-1,-1] → +h1-h0-h1
    % h_values(6): [+1,-1,+1] → +h1-h0+h1
    % h_values(7): [+1,+1,-1] → +h1+h0-h1
    % h_values(8): [+1,+1,+1] → +h1+h0+h1
    h_values = [-h1 - h0 - h1, -h1 - h0 + h1, -h1 + h0 - h1, -h1 + h0 + h1, ...
                 h1 - h0 - h1,  h1 - h0 + h1,  h1 + h0 - h1,  h1 + h0 + h1];

    INF = 1e5;  % 무한대 값 (초기화용)

    % 초기 경로 메트릭 설정
    % 상태: 00(0), 01(1), 10(2), 11(3)
    % AIS Start bit 패턴이 "01"로 끝나므로 상태 01만 0으로 초기화
    pm_init = [INF,  0, INF, INF];

    % 각 상태별 데이터 시퀀스 저장 변수 초기화
    data_seq_00_old = [];  % 상태 00으로 가는 경로의 비트 시퀀스
    data_seq_01_old = [];  % 상태 01로 가는 경로의 비트 시퀀스
    data_seq_10_old = [];  % 상태 10으로 가는 경로의 비트 시퀀스
    data_seq_11_old = [];  % 상태 11로 가는 경로의 비트 시퀀스

    % Viterbi 알고리즘 메인 루프: 각 시간 단계에서 최적 경로 계산
    for (idx = 1:length(rx_data))

        % ========== 상태 00 (이전 2비트: 00) 처리 ==========
        % 상태 00으로 들어올 수 있는 경로:
        % 1) 상태 00에서 비트 0 입력 (00 → 00)
        % 2) 상태 10에서 비트 0 입력 (10 → 00)

        % 브랜치 메트릭 계산 (수신신호와 예상신호의 거리 제곱)
        bm_00_0(idx) = (rx_data(idx) - h_values(1))^2;  % 상태 00→00 (비트패턴: 000)
        bm_10_0(idx) = (rx_data(idx) - h_values(5))^2;  % 상태 10→00 (비트패턴: 100)

        % 경로 메트릭 후보 계산
        if (idx == 1)
            % 첫 번째 샘플: 초기값 사용
            pm_cand1 = pm_init(1) + bm_00_0(idx);     % 상태 00에서 오는 경로
            pm_cand2 = pm_init(3) + bm_10_0(idx);     % 상태 10에서 오는 경로
        else
            % 이후 샘플: 이전 단계의 경로 메트릭 사용
            pm_cand1 = pm_00_old + bm_00_0(idx);      % 상태 00에서 오는 경로
            pm_cand2 = pm_10_old + bm_10_0(idx);      % 상태 10에서 오는 경로
        end

        % 생존 경로 선택 (Add-Compare-Select)
        if (pm_cand1 < pm_cand2)
            pm_00 = pm_cand1;                       % 더 작은 메트릭 선택
            data_seq_00 = [data_seq_00_old; 0];     % 비트 0(-1) 추가, 상태 00 경로 이어받기
        else
            pm_00 = pm_cand2;                       % 더 작은 메트릭 선택
            data_seq_00 = [data_seq_10_old; 0];     % 비트 0(-1) 추가, 상태 10 경로 이어받기
        end

        % ========== 상태 01 (이전 2비트: 01) 처리 ==========
        % 상태 01로 들어올 수 있는 경로:
        % 1) 상태 00에서 비트 1 입력 (00 → 01)
        % 2) 상태 10에서 비트 1 입력 (10 → 01)

        bm_00_1(idx) = (rx_data(idx) - h_values(2))^2;  % 상태 00→01 (비트패턴: 001)
        bm_10_1(idx) = (rx_data(idx) - h_values(6))^2;  % 상태 10→01 (비트패턴: 101)

        if (idx == 1)
            pm_cand1 = pm_init(1) + bm_00_1(idx);     % 상태 00에서 오는 경로
            pm_cand2 = pm_init(3) + bm_10_1(idx);     % 상태 10에서 오는 경로
        else
            pm_cand1 = pm_00_old + bm_00_1(idx);      % 상태 00에서 오는 경로
            pm_cand2 = pm_10_old + bm_10_1(idx);      % 상태 10에서 오는 경로
        end

        % 생존 경로 선택
        if (pm_cand1 < pm_cand2)
            pm_01 = pm_cand1;
            data_seq_01 = [data_seq_00_old; 1];     % 비트 1(+1) 추가, 상태 00 경로 이어받기
        else
            pm_01 = pm_cand2;
            data_seq_01 = [data_seq_10_old; 1];     % 비트 1(+1) 추가, 상태 10 경로 이어받기
        end

        % ========== 상태 10 (이전 2비트: 10) 처리 ==========
        % 상태 10으로 들어올 수 있는 경로:
        % 1) 상태 01에서 비트 0 입력 (01 → 10)
        % 2) 상태 11에서 비트 0 입력 (11 → 10)

        bm_01_0(idx) = (rx_data(idx) - h_values(3))^2;  % 상태 01→10 (비트패턴: 010)
        bm_11_0(idx) = (rx_data(idx) - h_values(7))^2;  % 상태 11→10 (비트패턴: 110)

        if (idx == 1)
            pm_cand1 = pm_init(2) + bm_01_0(idx);     % 상태 01에서 오는 경로
            pm_cand2 = pm_init(4) + bm_11_0(idx);     % 상태 11에서 오는 경로
        else
            pm_cand1 = pm_01_old + bm_01_0(idx);      % 상태 01에서 오는 경로
            pm_cand2 = pm_11_old + bm_11_0(idx);      % 상태 11에서 오는 경로
        end

        % 생존 경로 선택
        if (pm_cand1 < pm_cand2)
            pm_10 = pm_cand1;
            data_seq_10 = [data_seq_01_old; 0];    % 비트 0(-1) 추가, 상태 01 경로 이어받기
        else
            pm_10 = pm_cand2;
            data_seq_10 = [data_seq_11_old; 0];    % 비트 0(-1) 추가, 상태 11 경로 이어받기
        end

        % ========== 상태 11 (이전 2비트: 11) 처리 ==========
        % 상태 11로 들어올 수 있는 경로:
        % 1) 상태 01에서 비트 1 입력 (01 → 11)
        % 2) 상태 11에서 비트 1 입력 (11 → 11)

        bm_01_1(idx) = (rx_data(idx) - h_values(4))^2;  % 상태 01→11 (비트패턴: 011)
        bm_11_1(idx) = (rx_data(idx) - h_values(8))^2;  % 상태 11→11 (비트패턴: 111)

        if (idx == 1)
            pm_cand1 = pm_init(2) + bm_01_1(idx);     % 상태 01에서 오는 경로
            pm_cand2 = pm_init(4) + bm_11_1(idx);     % 상태 11에서 오는 경로
        else
            pm_cand1 = pm_01_old + bm_01_1(idx);      % 상태 01에서 오는 경로
            pm_cand2 = pm_11_old + bm_11_1(idx);      % 상태 11에서 오는 경로
        end

        % 생존 경로 선택
        if (pm_cand1 < pm_cand2)
            pm_11 = pm_cand1;
            data_seq_11 = [data_seq_01_old; 1];     % 비트 1(+1) 추가, 상태 01 경로 이어받기
        else
            pm_11 = pm_cand2;
            data_seq_11 = [data_seq_11_old; 1];     % 비트 1(+1) 추가, 상태 11 경로 이어받기
        end

        % ========== 다음 시간 단계를 위한 상태 업데이트 ==========
        % 현재 시간 단계의 경로 메트릭과 데이터 시퀀스를 저장
        pm_00_old = pm_00;                          % 상태 00의 경로 메트릭 저장
        pm_01_old = pm_01;                          % 상태 01의 경로 메트릭 저장
        pm_10_old = pm_10;                          % 상태 10의 경로 메트릭 저장
        pm_11_old = pm_11;                          % 상태 11의 경로 메트릭 저장
        data_seq_00_old = data_seq_00;              % 상태 00의 데이터 시퀀스 저장
        data_seq_01_old = data_seq_01;              % 상태 01의 데이터 시퀀스 저장
        data_seq_10_old = data_seq_10;              % 상태 10의 데이터 시퀀스 저장
        data_seq_11_old = data_seq_11;              % 상태 11의 데이터 시퀀스 저장
    end

    % 최종 결과: 상태 00에서 종료하는 최적 경로의 비트 시퀀스 반환
    % (AIS 프로토콜에서는 일반적으로 특정 상태에서 종료하도록 설계됨)
end


function [h, k] = gmsk_impulse_response(BT, OSR, LENGTH, H_NORMALIZATION)
% h: impulse response
% k: time indeces
    h = zeros(LENGTH, 1);
    k = (-LENGTH/2+1):(LENGTH/2);
    Ts = 1;
    h = 0.5*(erf(pi*BT*sqrt(2/log(2))*(k/OSR + 0.5)) - erf(pi*BT*sqrt(2/log(2))*(k/OSR - 0.5)));

    if H_NORMALIZATION == 1
        h = h/max(h);
    elseif H_NORMALIZATION == 2
        h = h/norm(h);
    end
end


function [h, k] = gauss_impulse_response(BT, OSR, LENGTH, H_NORMALIZATION)
% h: impulse response
% k: time indeces
%    BT = 0.4;
    h = zeros(LENGTH, 1);
    k = (-LENGTH/2+1):(LENGTH/2); %-3:3;
    Ts = 1;
    T = OSR; %2;
    h = 1*sqrt(2*pi/log(2))*exp(-2/log(2)*(BT*pi*(k/T)).^2); % from [svedek]
    if H_NORMALIZATION == 1
        h = h/max(h);
    elseif H_NORMALIZATION == 2
        h = h/norm(h);
    end
end

% calc_h0_and_bias: 채널 임펄스 응답의 최대값(h0)과 DC 바이어스를 추정하는 함수
%
% 입력 파라미터:
%   received_preamble_os    : 수신된 프리앰블 신호 (오버샘플링됨)
%   source_preamble_os      : 송신 프리앰블 신호 (오버샘플링됨, 기준 신호)
%   half_impulse_response_os: 반쪽 임펄스 응답 (채널 특성)
%   FILTERED_TWICE          : 이중 필터링 여부 플래그
%
% 출력 파라미터:
%   h0   : 채널 임펄스 응답의 최대값 (채널 게인)
%   bias : DC 바이어스 (신호의 직류 성분)
%
% 동작 원리:
% 1. 최소자승법(Least Squares)을 사용하여 수신 신호를 모델링
% 2. 수신신호 = h0 * 송신신호 + bias + 노이즈 형태로 가정
% 3. 의사역행렬(pseudo-inverse)을 이용해 h0와 bias를 동시에 추정
function [h0, bias] = calc_h0_and_bias(received_preamble_os, source_preamble_os, half_impulse_response_os, FILTERED_TWICE)

    % 필터링 횟수에 따른 시작 인덱스 설정
    % 이중 필터링 시 더 많은 지연이 발생하므로 시작점을 뒤로 이동
    if FILTERED_TWICE
        INDEX_START = 39;  % 이중 필터링: 39번째 샘플부터 시작
    else
        INDEX_START = 17;  % 단일 필터링: 20번째 샘플부터 시작
    end

    % 최소자승법을 위한 행렬 A 구성
    % A = [송신신호_정렬된부분, 1의벡터]
    % 수신신호 = A * [h0_scale; bias] 형태로 모델링
    A = [source_preamble_os(INDEX_START:INDEX_START+length(received_preamble_os)-1), ones(length(received_preamble_os),1)];

    % 의사역행렬(pseudo-inverse) 계산
    % pinv(A) = (A'*A)^(-1)*A' (최소자승법 해)
    pinv_data = pinv(A);

    % 계수 벡터 계산: [h0_scale; bias] = pinv(A) * received_signal
    coeff_vector = pinv_data*received_preamble_os;

    % h0 계산: 스케일 팩터에 임펄스 응답의 최대값을 곱해서 실제 채널 게인 구함
    h0 = coeff_vector(1)*max(half_impulse_response_os);

    % DC 바이어스 추출
    bias = coeff_vector(2);

    %-------------------------------------------------------------------------
    figure(2);
    kkk = 1:length(received_preamble_os);  % x축 인덱스
    scale = h0/max(half_impulse_response_os);  % 정규화된 스케일 팩터

    % 서브플롯 1: 수신된 프리앰블 신호
    subplot(3,1,1); plot(received_preamble_os); grid; title('received preamble oversampled signal');
    % 서브플롯 2: 정렬된 송신 프리앰블 신호
    subplot(3,1,2); plot(source_preamble_os(INDEX_START:INDEX_START+length(received_preamble_os)-1)); grid; title('source preamble oversampled signal');
    % 서브플롯 3: 신호 비교 (스케일된 송신신호, 바이어스 추가된 신호, 수신신호)
    subplot(3,1,3); plot(kkk, scale*source_preamble_os(INDEX_START:INDEX_START+length(received_preamble_os)-1), '-o', ...
                         kkk, scale*source_preamble_os(INDEX_START:INDEX_START+length(received_preamble_os)-1)-bias, '-x', ...
                         kkk, received_preamble_os, '-+');
                    grid; title('source preamble scaled signal(o), received preamble oversampled signal(+)');
    %-------------------------------------------------------------------------

    h0 = abs(h0);
    bias = abs(bias);
end

function half_impulse_response_os = convolved_half_impulse_response_os(filter_kind, BT1, filter_kind2, BT2, OSR, HALF_LENTH, H_NORMALIZATION)
    if filter_kind1 == 1 % gmsk
        [h1, k1] = gmsk_impulse_response(BT1, OSR, 2*HALF_LENTH - 1, H_NORMALIZATION);
    elseif filter_kind1 == 2 % gaussfilter
        [h1, k1] = gauss_impulse_response(BT1, OSR, 2*HALF_LENTH - 1, H_NORMALIZATION);
    end

    if filter_kind2 == 1 % gmsk
        [h2, k2] = gmsk_impulse_response(BT2, OSR, 2*HALF_LENTH - 1, H_NORMALIZATION);
    elseif filter_kind2 == 2 % gaussfilter
        [h2, k2] = gauss_impulse_response(BT2, OSR, 2*HALF_LENTH - 1, H_NORMALIZATION);
    end

    h_conv = conv (h1, h2);
    index_max = find (h_conv == max(h_conv));
    if H_NORMALIZATION == 1
        h_conv = h_conv/max(h_conv);
    elseif H_NORMALIZATION == 2
        h_conv = h_conv/norm(h_conv);
    end
    half_impulse_response_os = h_conv(index_max : (index_max + HALF_LENTH - 1));

    figure(111);
    kkk = 1:2*HALF_LENTH - 1;
    subplot(3,1,1); plot(kkk, h1, '-o', kkk, h2, '-x'); grid; title('h1 (o), h2 (x)');
    subplot(3,1,2); plot(h_conv, '-o'); grid; title('h\_conv');
    subplot(3,1,3); plot(half_impulse_response_os, '-o'); grid; title('half\_impulse\_response\_os');
end

% CRC 계산 (AIS 표준)
function crc = update_crc(crc, new_bit)
    if bitand(bitxor(crc, new_bit), 0x0001)
        crc = bitxor(bitshift(crc, -1), 0x8408);
    else
        crc = bitshift(crc, -1);
    end
end


%-------------------------------------------------------------------------
% Main function
%-------------------------------------------------------------------------
rawdata_len = length(source_data);
dc_level = zeros(rawdata_len, 1);
source_data_removed_dc = zeros(rawdata_len, 1);
downsampled_data = zeros(PACKET_PERIOD_OS, 1);

for p = 1:NUM_OF_PACKETS
    GLOBAL_RANGE_ONE_PERIOD = ((p - 1)*PACKET_PERIOD_OS + 1) : ((p - 1)*PACKET_PERIOD_OS + PACKET_PERIOD_OS);
    LOCAL_RANGE_ONE_PERIOD = 1 : PACKET_PERIOD_OS;
    LOCAL_RANGE_ONE_PERIOD_BAUD_RATE = 1 : PACKET_PERIOD;

    % dc levels (moving average)
    sum_x_100 = 0;
    for j = LOCAL_RANGE_ONE_PERIOD
        k = GLOBAL_RANGE_ONE_PERIOD(j);
        if j <= LEN_DOT_PATTERN*OSR
            sum_x_100 = sum_x_100 + source_data(k);
        else
            sum_x_100 = sum_x_100 + source_data(k) - source_data(k - LEN_DOT_PATTERN*OSR);
        end
        dc_level(k) = sum_x_100 / (LEN_DOT_PATTERN*OSR);
    end

    % correlations
    tmp_100 = [];
    for j = LOCAL_RANGE_ONE_PERIOD
        k = GLOBAL_RANGE_ONE_PERIOD(j);
        if j <= LEN_PREAMBLE*OSR
            correlation_value(k) = 0;
        else
            tmp_100 = source_data(k - LEN_PREAMBLE*OSR + 1 : k) - dc_level(k - LEN_PREAMBLE*OSR + 1 : k);
            tmp_100 = tmp_100/norm(tmp_100);
            correlation_value(k) = (tmp_100)' * preamble_os / norm(preamble_os);
            
        end
    end
    correlation_value(GLOBAL_RANGE_ONE_PERIOD) = 1000*abs(correlation_value(GLOBAL_RANGE_ONE_PERIOD));

    % find peak correlation value index in one period
    peak_correlation_index(p) = GLOBAL_RANGE_ONE_PERIOD(find(correlation_value(GLOBAL_RANGE_ONE_PERIOD) == max(correlation_value(GLOBAL_RANGE_ONE_PERIOD))));

    % dc_level_fixed
    DC_LEVEL_FIXED = dc_level(peak_correlation_index(p) - (LEN_START_PATTERN+1)*OSR);
    source_data_removed_dc = source_data - DC_LEVEL_FIXED;

    %-------------------------------------------------------------------------
    %   data input in this packet
    %-------------------------------------------------------------------------

    TIMING_OFFSET = -7;
    data_start_index(p) = peak_correlation_index(p) + TIMING_OFFSET + OSR;

    rx_data_of_last_preamble_bit = source_data_removed_dc(data_start_index(p) - OSR);
    for j = LOCAL_RANGE_ONE_PERIOD_BAUD_RATE
        downsampled_data(j) = source_data_removed_dc(data_start_index(p) + (j - 1)*OSR);
    end

    received_preamble_os = zeros(LEN_PREAMBLE_OS,1);
    for j = 1:LEN_PREAMBLE_OS
        received_preamble_os(j) = source_data_removed_dc(peak_correlation_index(p) - LEN_PREAMBLE_OS + (j - 1));
    end

    index_max = find(impulse_response_of_gmsk == max(impulse_response_of_gmsk));
    half_impulse_response_os = impulse_response_of_gmsk(index_max:(index_max+2*OSR));
    [h0, dc_bias] = calc_h0_and_bias(received_preamble_os, preamble_filtered_by_gmsk, half_impulse_response_os, 0);

    HALF_LENTH = fix(LEN_PSF/2);
    half_impulse_response_os = convolved_half_impulse_response_os(1, BT, BT, OSR, HALF_LENTH, 1);
    h0_for_mlsd = half_impulse_response_os(1) * h0 - dc_bias;
    h1_for_mlsd = half_impulse_response_os(OSR+1) * h0 - dc_bias;

    mlsd_detected_data = viterbi_mlsd ([rx_data_of_last_preamble_bit; downsampled_data], ...
                                        h0_for_mlsd, ...
                                        h1_for_mlsd);

    %G_wCrcRegData = 0xffff;
    for i = 2:length(mlsd_detected_data)
        if mlsd_detected_data(i-1) == mlsd_detected_data(i)
            packet_bit_data(i-1) = 1;
        else
            packet_bit_data(i-1) = 0;
        end

        %G_wCrcRegData = update_crc(G_wCrcRegData, packet_bit_data(i-1));
        %if(G_wCrcRegData == 0xf0b8)
        %    fprintf('CRC OK at %d\n', i-1);
        %end
    end

    for k = LOCAL_RANGE_ONE_PERIOD_BAUD_RATE
        if downsampled_data(k) > 0
            direct_decision_100(k) = 1;
        else
            direct_decision_100(k) = 0;
        end
    end

    %-------------------------------------------------------------------------
    figure(201);
    range1 = GLOBAL_RANGE_ONE_PERIOD;
    range2 = LOCAL_RANGE_ONE_PERIOD_BAUD_RATE;
    subplot(3,1,1); plot(range1, source_data(range1), '-x', range1, dc_level(range1), '--', range1, correlation_value(range1), 'r-'); title('correlation'); grid;
    subplot(3,1,2); plot(range2, downsampled_data(range2), '-x'); title('rx data'); grid;
    subplot(3,1,3); plot(range2, mlsd_detected_data(range2), '-+', range2, direct_decision_100(range2), '-o'); title('estimated sequence(+), direct sequence(o)'); grid;
    %-------------------------------------------------------------------------
end
