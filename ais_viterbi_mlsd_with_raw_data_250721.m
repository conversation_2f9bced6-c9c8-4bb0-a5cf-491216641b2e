clear all;

%-------------------------------------------------------------------------
% Raw data input
%-------------------------------------------------------------------------
fileID = fopen('Success_Packets_20250715_104824\Success_Packet_001.bin');   %5 %17 %33
%fileID = fopen('CRC_Error_Data_20250715_104824\CRC_Error_Packet_001.bin');   %9실패, 17,22복구성공
source_data = fread(fileID, 'uint16');

%-------------------------------------------------------------------------
% Defines
%-------------------------------------------------------------------------
OSR = 5;                                % Over sampling rate
BT = 0.4;                               % Bandwidth time

PACKET_PERIOD       = 198;              % Packet period
PACKET_PERIOD_OS    = PACKET_PERIOD*OSR;
LEN_PSF             = 8*OSR;            % Pulse shaping filter length

DC_LEVEL            = 0;                % DC level

INF                 = 1e5;              % 무한대 값 (초기화용)

MAX_SYNC_CORRVAL    = .750;
MAX_SYNC_COUNT      = 25;
TIMING_OFFSET       = -7;

data_start_index = 0;
data_count = 0;

%-------------------------------------------------------------------------
% Rx Operation Mode
%-------------------------------------------------------------------------
RX_MDM_STATUS_PREAMBLE  = 0;
RX_MDM_STATUS_START     = 1;
RX_MDM_STATUS_PRELOAD   = 2;
RX_MDM_STATUS_DATA      = 3;

G_wRxRunStatus          = RX_MDM_STATUS_PREAMBLE;
G_dMaxSyncCorrel        = 0;
G_dMaxSyncCnt           = 0;

%-------------------------------------------------------------------------
% Traning sequence(24bit : 0101...) and start flag(0x7E)
%-------------------------------------------------------------------------
dot_pattern       = repmat([-1, -1, 1, 1], 1, 6);   % Dot pattern
start_pattern     = [-1, -1, -1, -1, -1, -1, -1, 1];% Start pattern
preamble          = [dot_pattern, start_pattern]';  % Preamble
preamble_os       = repelem(preamble, OSR);         % Over sampled preamble
LEN_DOT_PATTERN   = length(dot_pattern);            % Length of dot pattern
LEN_START_PATTERN = length(start_pattern);          % Length of start pattern
LEN_PREAMBLE      = length(preamble);               % Length of preamble
LEN_PREAMBLE_OS   = LEN_PREAMBLE*OSR;               % Length of over sampled preamble

%-------------------------------------------------------------------------
% impulse response of gmsk, gauss filter
%-------------------------------------------------------------------------
[impulse_response_of_gmsk, kkk] = gmsk_impulse_response(BT, OSR, LEN_PSF, 2);
preamble_zero_padded            = upsample(preamble, OSR);
preamble_filtered_by_gmsk       = conv(preamble_zero_padded, impulse_response_of_gmsk);
preamble_range                  = 1:length(preamble_filtered_by_gmsk);

HALF_LENTH = fix(LEN_PSF/2);
half_impulse_response_os = convolved_half_impulse_response_os(1, BT, BT, OSR, HALF_LENTH, 1);

%-------------------------------------------------------------------------
% Viterbi 상태 정보를 모두 포함하는 통합 구조체
%-------------------------------------------------------------------------
viterbi_states = struct();

% 4개 상태에 대한 정보를 배열로 관리
viterbi_states.sequences = cell(4, 1);     % 비트 시퀀스
viterbi_states.path_metrics = zeros(4, 1); % 경로 메트릭

% 상태 매핑 정보
viterbi_states.state_map = {'00', '01', '10', '11'};

%-------------------------------------------------------------------------
figure(1);
subplot(4,1,1); plot(impulse_response_of_gmsk, '-o'); grid; title('impulse\_response (gmsk)');
subplot(4,1,2); plot(preamble, '-o'); grid; title('preamble');
subplot(4,1,3); plot(preamble_range, preamble_filtered_by_gmsk(preamble_range), '-o');
                    grid; title('preamble\_filtered (gmsk) (o), (gmsk twice) (x)');
subplot(4,1,4); plot(source_data, '-o'); grid; title('source\_data');
%-------------------------------------------------------------------------

%-------------------------------------------------------------------------
% functions
%-------------------------------------------------------------------------
% viterbi_mlsd
% Maximum Likelihood Sequence Detection (최대우도 시퀀스 검출) 함수

% Viterbi 알고리즘을 사용하여 ISI(Inter-Symbol Interference)가 있는 채널에서
% 최적의 비트 시퀀스를 복원하는 함수
%
% 입력 파라미터:
%   rx_data                       : 수신된 신호 데이터
%   h0                            : 채널 임펄스 응답의 중심값 (현재 심볼)
%   h1                            : 채널 임펄스 응답의 이전/이후 심볼 영향값
%   LOCAL_RANGE_ONE_PERIOD_BAUD_RATE : 처리할 샘플 범위
%
% 출력 파라미터:
%   data_seq_00                   : 복원된 비트 시퀀스 (상태 00에서 종료)
%
% 동작 원리:
% 1. 4개 상태 (00, 01, 10, 11)를 가진 트렐리스 구조 사용
% 2. 각 상태는 이전 2비트의 조합을 나타냄 (메모리 길이 = 2)
% 3. ISI를 고려한 8가지 예상 신호값을 미리 계산
% 4. Viterbi 알고리즘으로 최소 누적 메트릭 경로를 추적
function [viterbi_states] = viterbi_mlsd(sviterbi_states, rx_data, h0, h1)

    % ISI를 고려한 8가지 예상 신호 값 계산
    % 형태: [이전비트, 현재비트, 다음비트]의 조합에 따른 신호값
    % h_values(1): [-1,-1,-1] → -h1-h0-h1
    % h_values(2): [-1,-1,+1] → -h1-h0+h1
    % h_values(3): [-1,+1,-1] → -h1+h0-h1
    % h_values(4): [-1,+1,+1] → -h1+h0+h1
    % h_values(5): [+1,-1,-1] → +h1-h0-h1
    % h_values(6): [+1,-1,+1] → +h1-h0+h1
    % h_values(7): [+1,+1,-1] → +h1+h0-h1
    % h_values(8): [+1,+1,+1] → +h1+h0+h1
    h_values = [-h1 - h0 - h1, -h1 - h0 + h1, -h1 + h0 - h1, -h1 + h0 + h1, ...
                 h1 - h0 - h1,  h1 - h0 + h1,  h1 + h0 - h1,  h1 + h0 + h1];

    % ========== 상태 00 (이전 2비트: 00) 처리 ==========
    % 상태 00으로 들어올 수 있는 경로:
    % 1) 상태 00에서 비트 0 입력 (00 → 00)
    % 2) 상태 10에서 비트 0 입력 (10 → 00)

    % 브랜치 메트릭 계산 (수신신호와 예상신호의 거리 제곱)
    bm_00_0 = (rx_data - h_values(1))^2;  % 상태 00→00 (비트패턴: 000)
    bm_10_0 = (rx_data - h_values(5))^2;  % 상태 10→00 (비트패턴: 100)

    % 경로 메트릭 후보 계산
    pm_cand1 = viterbi_states.path_metrics{1} + bm_00_0;      % 상태 00에서 오는 경로
    pm_cand2 = viterbi_states.path_metrics{3} + bm_10_0;      % 상태 10에서 오는 경로

    % 생존 경로 선택 (Add-Compare-Select)
    if (pm_cand1 < pm_cand2)
        pm_00 = pm_cand1;                       % 더 작은 메트릭 선택
        data_seq_00 = [viterbi_states.sequences{1}; 0];     % 비트 0(-1) 추가, 상태 00 경로 이어받기
    else
        pm_00 = pm_cand2;                       % 더 작은 메트릭 선택
        data_seq_00 = [viterbi_states.sequences{3}; 0];     % 비트 0(-1) 추가, 상태 10 경로 이어받기
    end

    % ========== 상태 01 (이전 2비트: 01) 처리 ==========
    % 상태 01로 들어올 수 있는 경로:
    % 1) 상태 00에서 비트 1 입력 (00 → 01)
    % 2) 상태 10에서 비트 1 입력 (10 → 01)

    bm_00_1 = (rx_data - h_values(2))^2;  % 상태 00→01 (비트패턴: 001)
    bm_10_1 = (rx_data - h_values(6))^2;  % 상태 10→01 (비트패턴: 101)

    pm_cand1 = viterbi_states.path_metrics{1} + bm_00_1;      % 상태 00에서 오는 경로
    pm_cand2 = viterbi_states.path_metrics{3} + bm_10_1;      % 상태 10에서 오는 경로

    % 생존 경로 선택
    if (pm_cand1 < pm_cand2)
        pm_01 = pm_cand1;
        data_seq_01 = [viterbi_states.sequences{1}; 1];     % 비트 1(+1) 추가, 상태 00 경로 이어받기
    else
        pm_01 = pm_cand2;
        data_seq_01 = [viterbi_states.sequences{3}; 1];     % 비트 1(+1) 추가, 상태 10 경로 이어받기
    end

    % ========== 상태 10 (이전 2비트: 10) 처리 ==========
    % 상태 10으로 들어올 수 있는 경로:
    % 1) 상태 01에서 비트 0 입력 (01 → 10)
    % 2) 상태 11에서 비트 0 입력 (11 → 10)

    bm_01_0 = (rx_data - h_values(3))^2;  % 상태 01→10 (비트패턴: 010)
    bm_11_0 = (rx_data - h_values(7))^2;  % 상태 11→10 (비트패턴: 110)

    pm_cand1 = viterbi_states.path_metrics{2} + bm_01_0;      % 상태 01에서 오는 경로
    pm_cand2 = viterbi_states.path_metrics{4} + bm_11_0;      % 상태 11에서 오는 경로

    % 생존 경로 선택
    if (pm_cand1 < pm_cand2)
        pm_10 = pm_cand1;
        data_seq_10 = [viterbi_states.sequences{2}; 0];    % 비트 0(-1) 추가, 상태 01 경로 이어받기
    else
        pm_10 = pm_cand2;
        data_seq_10 = [viterbi_states.sequences{4}; 0];    % 비트 0(-1) 추가, 상태 11 경로 이어받기
    end

    % ========== 상태 11 (이전 2비트: 11) 처리 ==========
    % 상태 11로 들어올 수 있는 경로:
    % 1) 상태 01에서 비트 1 입력 (01 → 11)
    % 2) 상태 11에서 비트 1 입력 (11 → 11)

    bm_01_1 = (rx_data - h_values(4))^2;  % 상태 01→11 (비트패턴: 011)
    bm_11_1 = (rx_data - h_values(8))^2;  % 상태 11→11 (비트패턴: 111)

    pm_cand1 = viterbi_states.path_metrics{2} + bm_01_1;      % 상태 01에서 오는 경로
    pm_cand2 = viterbi_states.path_metrics{4} + bm_11_1;      % 상태 11에서 오는 경로

    % 생존 경로 선택
    if (pm_cand1 < pm_cand2)
        pm_11 = pm_cand1;
        data_seq_11 = [viterbi_states.sequences{2}; 1];     % 비트 1(+1) 추가, 상태 01 경로 이어받기
    else
        pm_11 = pm_cand2;
        data_seq_11 = [viterbi_states.sequences{4}; 1];     % 비트 1(+1) 추가, 상태 11 경로 이어받기
    end

    % ========== 다음 시간 단계를 위한 상태 업데이트 ==========
    % 현재 시간 단계의 경로 메트릭과 데이터 시퀀스를 저장
    viterbi_states.path_metrics{1} = pm_00;                 % 상태 00의 경로 메트릭 저장
    viterbi_states.path_metrics{2} = pm_01;                 % 상태 01의 경로 메트릭 저장
    viterbi_states.path_metrics{3} = pm_10;                 % 상태 10의 경로 메트릭 저장
    viterbi_states.path_metrics{4} = pm_11;                 % 상태 11의 경로 메트릭 저장
    viterbi_states.sequences{1} = data_seq_00;              % 상태 00의 데이터 시퀀스 저장
    viterbi_states.sequences{2} = data_seq_01;              % 상태 01의 데이터 시퀀스 저장
    viterbi_states.sequences{3} = data_seq_10;              % 상태 10의 데이터 시퀀스 저장
    viterbi_states.sequences{4} = data_seq_11;              % 상태 11의 데이터 시퀀스 저장
end


% GMSK 임펄스 응답 생성 함수
%
% 입력 파라미터:
%   BT              - 대역폭-시간 곱 (Bandwidth-Time product)
%   OSR             - 오버샘플링 비율 (Over Sampling Rate)
%   LENGTH          - 임펄스 응답의 길이 (샘플 수)
%   H_NORMALIZATION - 정규화 방법 (1: 최대값 정규화, 2: 노름 정규화)
%
% 출력 파라미터:
%   h               - GMSK 임펄스 응답 벡터
%   k               - 시간 인덱스 벡터
%
% 기능:
%   GMSK(Gaussian Minimum Shift Keying) 변조에 사용되는 임펄스 응답을 생성
%   오차함수(erf)를 이용하여 GMSK 필터의 임펄스 응답을 계산
%   AIS 신호의 GMSK 변조 특성을 모델링하기 위해 사용
function [h, k] = gmsk_impulse_response(BT, OSR, LENGTH, H_NORMALIZATION)
    % 임펄스 응답 벡터 초기화
    h = zeros(LENGTH, 1);

    % 시간 인덱스 생성 (중심을 0으로 하는 대칭 구조)
    k = (-LENGTH/2+1):(LENGTH/2);

    % 심볼 주기 설정
    Ts = 1;

    % GMSK 임펄스 응답 계산 (오차함수 기반)
    % 공식: h(t) = 0.5 * [erf(α(t+0.5T)) - erf(α(t-0.5T))]
    % 여기서 α = π*BT*sqrt(2/ln(2))
    h = 0.5*(erf(pi*BT*sqrt(2/log(2))*(k/OSR + 0.5)) - erf(pi*BT*sqrt(2/log(2))*(k/OSR - 0.5)));

    % 정규화 처리
    if H_NORMALIZATION == 1
        h = h/max(h);           % 최대값으로 정규화
    elseif H_NORMALIZATION == 2
        h = h/norm(h);          % 유클리드 노름으로 정규화
    end
end


% Gaussian 임펄스 응답 생성 함수
%
% 입력 파라미터:
%   BT              - 대역폭-시간 곱 (Bandwidth-Time product)
%   OSR             - 오버샘플링 비율 (Over Sampling Rate)
%   LENGTH          - 임펄스 응답의 길이 (샘플 수)
%   H_NORMALIZATION - 정규화 방법 (1: 최대값 정규화, 2: 노름 정규화)
%
% 출력 파라미터:
%   h               - Gaussian 임펄스 응답 벡터
%   k               - 시간 인덱스 벡터
%
% 기능:
%   Gaussian 필터의 임펄스 응답을 생성
%   가우시안 함수를 이용하여 필터 특성을 모델링
%   GMSK 변조의 기본이 되는 Gaussian 펄스 성형 필터 구현
function [h, k] = gauss_impulse_response(BT, OSR, LENGTH, H_NORMALIZATION)
    % 임펄스 응답 벡터 초기화
    h = zeros(LENGTH, 1);

    % 시간 인덱스 생성 (중심을 0으로 하는 대칭 구조)
    k = (-LENGTH/2+1):(LENGTH/2);

    % 심볼 주기 설정
    Ts = 1;
    T = OSR;  % 오버샘플링을 고려한 시간 스케일

    % Gaussian 임펄스 응답 계산 (Svedek 공식 기반)
    % 공식: h(t) = sqrt(2π/ln(2)) * exp(-2/ln(2) * (BT*π*t)^2)
    h = 1*sqrt(2*pi/log(2))*exp(-2/log(2)*(BT*pi*(k/T)).^2);

    % 정규화 처리
    if H_NORMALIZATION == 1
        h = h/max(h);           % 최대값으로 정규화
    elseif H_NORMALIZATION == 2
        h = h/norm(h);          % 유클리드 노름으로 정규화
    end
end

% calc_h0_and_bias: 채널 임펄스 응답의 최대값(h0)과 DC 바이어스를 추정하는 함수
%
% 입력 파라미터:
%   received_preamble_os    : 수신된 프리앰블 신호 (오버샘플링됨)
%   source_preamble_os      : 송신 프리앰블 신호 (오버샘플링됨, 기준 신호)
%   half_impulse_response_os: 반쪽 임펄스 응답 (채널 특성)
%   FILTERED_TWICE          : 이중 필터링 여부 플래그
%
% 출력 파라미터:
%   h0   : 채널 임펄스 응답의 최대값 (채널 게인)
%   bias : DC 바이어스 (신호의 직류 성분)
%
% 동작 원리:
% 1. 최소자승법(Least Squares)을 사용하여 수신 신호를 모델링
% 2. 수신신호 = h0 * 송신신호 + bias + 노이즈 형태로 가정
% 3. 의사역행렬(pseudo-inverse)을 이용해 h0와 bias를 동시에 추정
function [h0, bias] = calc_h0_and_bias(received_preamble_os, source_preamble_os, half_impulse_response_os, FILTERED_TWICE)

    % 필터링 횟수에 따른 시작 인덱스 설정
    % 이중 필터링 시 더 많은 지연이 발생하므로 시작점을 뒤로 이동
    if FILTERED_TWICE
        INDEX_START = 39;  % 이중 필터링: 39번째 샘플부터 시작
    else
        INDEX_START = 17;  % 단일 필터링: 20번째 샘플부터 시작
    end

    % 최소자승법을 위한 행렬 A 구성
    % A = [송신신호_정렬된부분, 1의벡터]
    % 수신신호 = A * [h0_scale; bias] 형태로 모델링
    A = [source_preamble_os(INDEX_START:INDEX_START+length(received_preamble_os)-1), ones(length(received_preamble_os),1)];

    % 의사역행렬(pseudo-inverse) 계산
    % pinv(A) = (A'*A)^(-1)*A' (최소자승법 해)
    pinv_data = pinv(A);

    % 계수 벡터 계산: [h0_scale; bias] = pinv(A) * received_signal
    coeff_vector = pinv_data*received_preamble_os;

    % h0 계산: 스케일 팩터에 임펄스 응답의 최대값을 곱해서 실제 채널 게인 구함
    h0 = coeff_vector(1)*max(half_impulse_response_os);

    % DC 바이어스 추출
    bias = coeff_vector(2);

    %-------------------------------------------------------------------------
    figure(2);
    kkk = 1:length(received_preamble_os);  % x축 인덱스
    scale = h0/max(half_impulse_response_os);  % 정규화된 스케일 팩터

    % 서브플롯 1: 수신된 프리앰블 신호
    subplot(3,1,1); plot(received_preamble_os); grid; title('received preamble oversampled signal');
    % 서브플롯 2: 정렬된 송신 프리앰블 신호
    subplot(3,1,2); plot(source_preamble_os(INDEX_START:INDEX_START+length(received_preamble_os)-1)); grid; title('source preamble oversampled signal');
    % 서브플롯 3: 신호 비교 (스케일된 송신신호, 바이어스 추가된 신호, 수신신호)
    subplot(3,1,3); plot(kkk, scale*source_preamble_os(INDEX_START:INDEX_START+length(received_preamble_os)-1), '-o', ...
                         kkk, scale*source_preamble_os(INDEX_START:INDEX_START+length(received_preamble_os)-1)-bias, '-x', ...
                         kkk, received_preamble_os, '-+');
                    grid; title('source preamble scaled signal(o), received preamble oversampled signal(+)');
    %-------------------------------------------------------------------------

    h0 = abs(h0);
    bias = abs(bias);
end

% 컨볼루션된 반 임펄스 응답 생성 함수 (오버샘플링 적용)
%
% 입력 파라미터:
%   filter_kind      - 필터 종류 (1: GMSK, 기타: Gaussian 필터)
%   BT1              - 첫 번째 필터의 BT 값 (대역폭-시간 곱)
%   BT2              - 두 번째 필터의 BT 값 (대역폭-시간 곱)
%   OSR              - 오버샘플링 비율 (Over Sampling Rate)
%   HALF_LENTH       - 반 임펄스 응답의 길이
%   H_NORMALIZATION  - 정규화 방법 (1: 최대값 정규화, 2: 노름 정규화)
%
% 출력:
%   half_impulse_response_os - 컨볼루션된 반 임펄스 응답 (오버샘플링 적용)
%
% 기능:
%   두 개의 임펄스 응답을 생성하고 컨볼루션하여 최종 반 임펄스 응답을 생성
%   Viterbi MLSD 알고리즘에서 사용되는 채널 모델링을 위한 함수
function half_impulse_response_os = convolved_half_impulse_response_os(filter_kind, BT1, BT2, OSR, HALF_LENTH, H_NORMALIZATION)
    % 필터 종류에 따라 임펄스 응답 생성
    if filter_kind == 1 % GMSK 필터 사용
        [h1, k1] = gmsk_impulse_response(BT1, OSR, 2*HALF_LENTH - 1, H_NORMALIZATION);
        [h2, k2] = gmsk_impulse_response(BT2, OSR, 2*HALF_LENTH - 1, H_NORMALIZATION);
    else                % Gaussian 필터 사용
        [h1, k1] = gauss_impulse_response(BT1, OSR, 2*HALF_LENTH - 1, H_NORMALIZATION);
        [h2, k2] = gauss_impulse_response(BT2, OSR, 2*HALF_LENTH - 1, H_NORMALIZATION);
    end

    % 두 임펄스 응답의 컨볼루션 수행
    h_conv = conv (h1, h2);

    % 컨볼루션 결과에서 최대값의 인덱스 찾기 (피크 위치)
    index_max = find (h_conv == max(h_conv));

    % 정규화 방법에 따른 처리
    if H_NORMALIZATION == 1
        h_conv = h_conv/max(h_conv);        % 최대값으로 정규화
    elseif H_NORMALIZATION == 2
        h_conv = h_conv/norm(h_conv);       % 유클리드 노름으로 정규화
    end

    % 최대값 위치부터 HALF_LENTH 길이만큼 추출하여 반 임펄스 응답 생성
    half_impulse_response_os = h_conv(index_max : (index_max + HALF_LENTH - 1));

    % 디버깅 및 시각화를 위한 플롯 생성
    figure(111);
    kkk = 1:2*HALF_LENTH - 1;
    subplot(3,1,1); plot(kkk, h1, '-o', kkk, h2, '-x'); grid; title('h1 (o), h2 (x)');
    subplot(3,1,2); plot(h_conv, '-o'); grid; title('h\_conv');
    subplot(3,1,3); plot(half_impulse_response_os, '-o'); grid; title('half\_impulse\_response\_os');
end

% CRC 계산 (AIS 표준)
function crc = update_crc(crc, new_bit)
    if bitand(bitxor(crc, new_bit), 0x0001)
        crc = bitxor(bitshift(crc, -1), 0x8408);
    else
        crc = bitshift(crc, -1);
    end
end


%-------------------------------------------------------------------------
% Main function
%-------------------------------------------------------------------------
rawdata_len = length(source_data);
dc_level = zeros(rawdata_len, 1);
source_data_removed_dc = zeros(rawdata_len, 1);
downsampled_data = zeros(PACKET_PERIOD_OS, 1);

dc_level_sum = 0;
correlation_value = zeros(rawdata_len, 1);
rawdata_excluded_dc = zeros(LEN_PREAMBLE*OSR, 1);
received_preamble_os = zeros(LEN_PREAMBLE_OS,1);

for symbol_index = 1:rawdata_len
    GLOBAL_RANGE_ONE_PERIOD = ((symbol_index - 1)*PACKET_PERIOD_OS + 1) : ((symbol_index - 1)*PACKET_PERIOD_OS + PACKET_PERIOD_OS);
    LOCAL_RANGE_ONE_PERIOD = 1 : PACKET_PERIOD_OS;
    LOCAL_RANGE_ONE_PERIOD_BAUD_RATE = 1 : PACKET_PERIOD;

    if (G_wRxRunStatus == RX_MDM_STATUS_PREAMBLE)
        % dc levels (moving average)
        if symbol_index <= LEN_DOT_PATTERN*OSR
            dc_level_sum = dc_level_sum + source_data(symbol_index);
        else
            dc_level_sum = dc_level_sum + source_data(symbol_index) - source_data(symbol_index - LEN_DOT_PATTERN*OSR);
        end
        dc_level(symbol_index) = dc_level_sum / (LEN_DOT_PATTERN*OSR);

        % correlations
        if symbol_index <= LEN_PREAMBLE*OSR
            correlation_value(symbol_index) = 0;
        else
            rawdata_excluded_dc = source_data(symbol_index - LEN_PREAMBLE*OSR + 1 : symbol_index) - dc_level(symbol_index - LEN_PREAMBLE*OSR + 1 : symbol_index);
            rawdata_excluded_dc = rawdata_excluded_dc/norm(rawdata_excluded_dc);
            correlation_value(symbol_index) = abs( (rawdata_excluded_dc)' * preamble_os / norm(preamble_os));
            
        end

        if (correlation_value(symbol_index) > MAX_SYNC_CORRVAL && correlation_value(symbol_index) > G_dMaxSyncCorrel)
            G_dMaxSyncCorrel = correlation_value(symbol_index);
            G_dMaxSyncCnt = 0;
            peak_correlation_index = symbol_index;
            data_start_index = symbol_index + TIMING_OFFSET;
        elseif (G_dMaxSyncCorrel > MAX_SYNC_CORRVAL)
            G_dMaxSyncCnt = G_dMaxSyncCnt + 1;

            if (G_dMaxSyncCnt >= MAX_SYNC_COUNT)
                G_wRxRunStatus = RX_MDM_STATUS_START;

                % dc_level_fixed
                DC_LEVEL_FIXED = dc_level(symbol_index - (LEN_START_PATTERN+1)*OSR);

                for idx = 1:LEN_PREAMBLE_OS
                    received_preamble_os(idx) = source_data(peak_correlation_index - LEN_PREAMBLE_OS + (idx - 1)) - DC_LEVEL_FIXED;
                end

                % h0 and dc_bias
                [h0, dc_bias] = calc_h0_and_bias(received_preamble_os, preamble_filtered_by_gmsk, impulse_response_of_gmsk, 0);

                % h0_for_mlsd and h1_for_mlsd (half_impulse_response_os)
                h0_for_mlsd = half_impulse_response_os(1) * h0 - dc_bias;
                h1_for_mlsd = half_impulse_response_os(OSR+1) * h0 - dc_bias;

                % viterbi_states 초기화
                for i = 1:4
                    viterbi_states.sequences{i} = [];       % 빈 시퀀스로 초기화

                    % 경로 메트릭 초기화
                    % AIS Start bit 패턴이 "01"로 끝나므로 상태 01만 0으로 초기화
                    if (i == 2)
                        viterbi_states.path_metrics(i) = 0;
                    else
                        viterbi_states.path_metrics(i) = INF;
                    end
                end
            end
        else
            G_dMaxSyncCorrel = 0;
            G_dMaxSyncCnt = 0;
            peak_correlation_index = 0;
            data_start_index = 0;
            data_count = 0;
        end
    else
        if (data_start_index <= symbol_index)
            % Viterbi MLSD
            viterbi_states = viterbi_mlsd(viterbi_states, source_data(data_start_index) - DC_LEVEL_FIXED, h0_for_mlsd, h1_for_mlsd);
            data_start_index = data_start_index + OSR;
            data_count = data_count + 1;

            if (data_count == PACKET_PERIOD)
                G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                data_count = 0;
            end
        end
    end

%{
    %G_wCrcRegData = 0xffff;
    for i = 2:length(mlsd_detected_data)
        if mlsd_detected_data(i-1) == mlsd_detected_data(i)
            packet_bit_data(i-1) = 1;
        else
            packet_bit_data(i-1) = 0;
        end

        %G_wCrcRegData = update_crc(G_wCrcRegData, packet_bit_data(i-1));
        %if(G_wCrcRegData == 0xf0b8)
        %    fprintf('CRC OK at %d\n', i-1);
        %end
    end

    for k = LOCAL_RANGE_ONE_PERIOD_BAUD_RATE
        if downsampled_data(k) > 0
            direct_decision_100(k) = 1;
        else
            direct_decision_100(k) = 0;
        end
    end

    %-------------------------------------------------------------------------
    figure(201);
    range1 = GLOBAL_RANGE_ONE_PERIOD;
    range2 = LOCAL_RANGE_ONE_PERIOD_BAUD_RATE;
    subplot(3,1,1); plot(range1, source_data(range1), '-x', range1, dc_level(range1), '--', range1, correlation_value(range1), 'r-'); title('correlation'); grid;
    subplot(3,1,2); plot(range2, downsampled_data(range2), '-x'); title('rx data'); grid;
    subplot(3,1,3); plot(range2, mlsd_detected_data(range2), '-+', range2, direct_decision_100(range2), '-o'); title('estimated sequence(+), direct sequence(o)'); grid;
    %-------------------------------------------------------------------------
%}
end
